/**
 * Replicate 图像模型参数配置
 */

import { 
  ModelParameterConfig, 
  ASPECT_RATIO_OPTIONS, 
  VARIANTS_OPTIONS, 
  OUTPUT_FORMAT_OPTIONS,
  MEGAPIXELS_OPTIONS,
  BOOLEAN_OPTIONS
} from '../../parameter-types';

export const REPLICATE_IMAGE_MODELS: ModelParameterConfig[] = [
  {
    modelId: 'black-forest-labs/flux-krea-dev',
    version: '1.0',
    provider: 'replicate',
    modelType: 'image',
    parameters: [
      {
        name: 'aspect_ratio',
        type: 'select',
        required: false,
        default: '1:1',
        options: [
          ...ASPECT_RATIO_OPTIONS,
          { value: '21:9', label: '超宽屏 (21:9)', description: '电影级超宽比例' },
          { value: '9:21', label: '超高 (9:21)', description: '超高竖版比例' }
        ],
        description: '宽高比',
        tooltip: '选择生成图片的宽高比例',
        group: 'basic'
      },
      {
        name: 'num_outputs',
        type: 'select',
        required: false,
        default: '1',
        options: VARIANTS_OPTIONS,
        description: '生成数量',
        tooltip: '选择生成图片的数量',
        group: 'basic'
      },
      {
        name: 'output_format',
        type: 'select',
        required: false,
        default: 'webp',
        options: OUTPUT_FORMAT_OPTIONS,
        description: '输出格式',
        tooltip: '选择生成图片的文件格式',
        group: 'basic'
      },
      {
        name: 'output_quality',
        type: 'number',
        required: false,
        default: 80,
        min: 0,
        max: 100,
        description: '输出质量',
        tooltip: '图片质量，0-100，100为最高质量（不适用于PNG）',
        group: 'advanced'
      },
      {
        name: 'guidance',
        type: 'number',
        required: false,
        default: 3,
        min: 0,
        max: 10,
        step: 0.1,
        description: '引导强度',
        tooltip: '控制生成图片对提示词的遵循程度，数值越高越严格',
        group: 'advanced'
      },
      {
        name: 'num_inference_steps',
        type: 'number',
        required: false,
        default: 28,
        min: 1,
        max: 50,
        description: '推理步数',
        tooltip: '去噪步数，推荐28-50，步数越多质量越高但速度越慢',
        group: 'expert'
      },
      {
        name: 'seed',
        type: 'number',
        required: false,
        default: undefined,
        min: 0,
        max: 2147483647,
        description: '随机种子',
        tooltip: '设置随机种子以获得可重现的结果，留空则随机',
        group: 'expert'
      },
      {
        name: 'prompt_strength',
        type: 'number',
        required: false,
        default: 0.8,
        min: 0,
        max: 1,
        step: 0.1,
        description: '提示词强度',
        tooltip: '使用图片输入时的提示词强度，1.0表示完全重新生成',
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'megapixels',
        type: 'select',
        required: false,
        default: '1',
        options: MEGAPIXELS_OPTIONS,
        description: '图片尺寸',
        tooltip: '控制生成图片的分辨率',
        group: 'advanced'
      },
      {
        name: 'go_fast',
        type: 'select',
        required: false,
        default: 'true',
        options: [
          { value: 'true', label: '启用快速模式', description: '使用fp8量化，速度更快' },
          { value: 'false', label: '禁用快速模式', description: '使用bf16精度，结果更确定' }
        ],
        description: '快速模式',
        tooltip: '启用快速模式可以加速生成，但可能影响质量',
        group: 'expert'
      },
      {
        name: 'disable_safety_checker',
        type: 'select',
        required: false,
        default: 'false',
        options: [
          { value: 'false', label: '启用安全检查', description: '开启内容安全检查' },
          { value: 'true', label: '禁用安全检查', description: '关闭内容安全检查' }
        ],
        description: '安全检查',
        tooltip: '是否禁用生成图片的安全检查',
        group: 'expert'
      }
    ],
    parameterGroups: {
      basic: ['aspect_ratio', 'num_outputs', 'output_format'],
      advanced: ['output_quality', 'guidance', 'prompt_strength', 'megapixels'],
      expert: ['num_inference_steps', 'seed', 'go_fast', 'disable_safety_checker']
    }
  }
];
