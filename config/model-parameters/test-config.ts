/**
 * 测试模型参数配置是否正确加载
 * 这个文件用于开发时验证配置，不会在生产环境使用
 */

import { 
  getModelParameterConfig, 
  getAllSupportedModelIds,
  getModelDefaultParameters,
  validateParameterValue
} from './index';

/**
 * 测试所有模型配置是否正确加载
 */
export function testAllModelConfigs() {
  console.log('🧪 开始测试模型参数配置...');
  
  const supportedModels = getAllSupportedModelIds();
  console.log(`📋 支持的模型数量: ${supportedModels.length}`);
  console.log(`📋 支持的模型列表:`, supportedModels);
  
  let successCount = 0;
  let errorCount = 0;
  
  supportedModels.forEach(modelId => {
    try {
      const config = getModelParameterConfig(modelId);
      if (!config) {
        console.error(`❌ 模型 ${modelId} 配置加载失败`);
        errorCount++;
        return;
      }
      
      console.log(`✅ 模型 ${modelId} (${config.provider}/${config.modelType}) - ${config.parameters.length} 个参数`);
      
      // 测试默认值
      const defaults = getModelDefaultParameters(modelId);
      console.log(`   默认参数:`, defaults);
      
      // 测试参数验证
      config.parameters.forEach(param => {
        if (param.default !== undefined) {
          const validation = validateParameterValue(modelId, param.name, param.default);
          if (!validation.valid) {
            console.warn(`⚠️  模型 ${modelId} 参数 ${param.name} 默认值验证失败: ${validation.error}`);
          }
        }
      });
      
      successCount++;
    } catch (error) {
      console.error(`❌ 模型 ${modelId} 配置测试失败:`, error);
      errorCount++;
    }
  });
  
  console.log(`\n📊 测试结果: ${successCount} 成功, ${errorCount} 失败`);
  
  if (errorCount === 0) {
    console.log('🎉 所有模型配置测试通过！');
  } else {
    console.log('⚠️  存在配置错误，请检查上述错误信息');
  }
  
  return { successCount, errorCount };
}

/**
 * 测试特定模型的配置
 */
export function testModelConfig(modelId: string) {
  console.log(`🧪 测试模型 ${modelId} 的配置...`);
  
  const config = getModelParameterConfig(modelId);
  if (!config) {
    console.error(`❌ 模型 ${modelId} 配置不存在`);
    return false;
  }
  
  console.log(`📋 模型信息:`, {
    modelId: config.modelId,
    provider: config.provider,
    modelType: config.modelType,
    version: config.version,
    parameterCount: config.parameters.length
  });
  
  console.log(`📋 参数分组:`, config.parameterGroups);
  
  config.parameters.forEach(param => {
    console.log(`📋 参数 ${param.name}:`, {
      type: param.type,
      required: param.required,
      default: param.default,
      group: param.group,
      description: param.description
    });
    
    if (param.options) {
      console.log(`   选项:`, param.options.map(opt => `${opt.value}(${opt.label})`));
    }
    
    if (param.condition) {
      console.log(`   条件:`, param.condition);
    }
  });
  
  return true;
}

/**
 * 生成配置统计信息
 */
export function generateConfigStats() {
  const supportedModels = getAllSupportedModelIds();
  const stats = {
    totalModels: supportedModels.length,
    byProvider: {} as Record<string, number>,
    byType: {} as Record<string, number>,
    totalParameters: 0,
    parameterTypes: {} as Record<string, number>
  };
  
  supportedModels.forEach(modelId => {
    const config = getModelParameterConfig(modelId);
    if (!config) return;
    
    // 按provider统计
    stats.byProvider[config.provider] = (stats.byProvider[config.provider] || 0) + 1;
    
    // 按类型统计
    stats.byType[config.modelType] = (stats.byType[config.modelType] || 0) + 1;
    
    // 参数统计
    stats.totalParameters += config.parameters.length;
    
    config.parameters.forEach(param => {
      stats.parameterTypes[param.type] = (stats.parameterTypes[param.type] || 0) + 1;
    });
  });
  
  console.log('📊 配置统计信息:');
  console.log(`   总模型数: ${stats.totalModels}`);
  console.log(`   按Provider分布:`, stats.byProvider);
  console.log(`   按类型分布:`, stats.byType);
  console.log(`   总参数数: ${stats.totalParameters}`);
  console.log(`   参数类型分布:`, stats.parameterTypes);
  
  return stats;
}

// 如果直接运行此文件，执行测试
if (typeof window === 'undefined' && require.main === module) {
  testAllModelConfigs();
  generateConfigStats();
}
