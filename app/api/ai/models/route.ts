import { respData, respErr } from "@/lib/resp";
import { getLocalActiveAIModels, getLocalAIModelsByType } from "@/data/ai-models-local";



export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const type = url.searchParams.get('type');
    const locale = url.searchParams.get('locale') || 'en';

    let models;
    if (type) {
      // 获取特定类型的模型（本地版本）
      models = getLocalAIModelsByType(type, locale);
    } else {
      // 获取所有活跃模型（本地版本）
      models = getLocalActiveAIModels(locale);
    }

    // 按类型分组
    const groupedModels = models.reduce((acc, model) => {
      if (!acc[model.model_type]) {
        acc[model.model_type] = [];
      }
      acc[model.model_type].push(model);
      return acc;
    }, {} as Record<string, typeof models>);

    return respData({
      models,
      grouped: groupedModels,
      total: models.length
    });
  } catch (error) {
    console.error("Failed to fetch models:", error);
    return respErr("Failed to fetch models");
  }
}
