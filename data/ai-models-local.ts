/**
 * 本地化AI模型数据
 * 从数据库迁移到本地存储，提升性能和可用性
 */

export interface LocalAIModel {
  id: number;
  model_id: string;
  model_name: string;
  model_type: 'text' | 'image' | 'video' | 'multimodal';
  provider: string;
  api_endpoint: string;
  credits_per_unit: number;
  unit_type: string;
  is_active: boolean;
  description?: string;
  description_i18n?: Record<string, string>;
  model_name_i18n?: Record<string, string>;
  max_input_size?: number;
  supported_features?: string[];
  icon?: string;
  created_at: string;
  updated_at: string;
}

/**
 * 本地化AI模型数据
 */
export const LOCAL_AI_MODELS: LocalAIModel[] = [
  // 文本生成模型
  {
    id: 3,
    model_id: "gemini-2.5-flash-lite",
    model_name: "Gemini 2.5 Flash Lite",
    model_type: "text",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 2,
    unit_type: "tokens",
    is_active: true,
    description: "轻量级对话模型，成本低廉，功能基础",
    description_i18n: {
      "de": "Lightweight conversational model with low cost and basic features",
      "en": "Lightweight conversational model with low cost and basic features",
      "es": "Lightweight conversational model with low cost and basic features",
      "fr": "Lightweight conversational model with low cost and basic features",
      "it": "Lightweight conversational model with low cost and basic features",
      "ja": "低コストで基本機能を持つ軽量対話モデル",
      "ko": "Lightweight conversational model with low cost and basic features",
      "pt": "Lightweight conversational model with low cost and basic features",
      "ru": "Lightweight conversational model with low cost and basic features",
      "zh": "轻量级对话模型，成本低廉，功能基础"
    },
    model_name_i18n: {
      "de": "Gemini 2.5 Flash Lite",
      "en": "Gemini 2.5 Flash Lite",
      "es": "Gemini 2.5 Flash Lite",
      "fr": "Gemini 2.5 Flash Lite",
      "it": "Gemini 2.5 Flash Lite",
      "ja": "Gemini 2.5 ライト",
      "ko": "Gemini 2.5 Flash Lite",
      "pt": "Gemini 2.5 Flash Lite",
      "ru": "Gemini 2.5 Flash Lite",
      "zh": "Gemini 2.5 轻量版"
    },
    max_input_size: 64000,
    supported_features: ["text_generation", "basic_conversation"],
    icon: "/imgs/icons/google.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 2,
    model_id: "gemini-2.5-flash",
    model_name: "Gemini 2.5 Flash",
    model_type: "text",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 5,
    unit_type: "tokens",
    is_active: true,
    description: "快速对话模型，响应迅速，效率高",
    description_i18n: {
      "de": "Fast conversational model with quick responses and high efficiency",
      "en": "Fast conversational model with quick responses and high efficiency",
      "es": "Fast conversational model with quick responses and high efficiency",
      "fr": "Fast conversational model with quick responses and high efficiency",
      "it": "Fast conversational model with quick responses and high efficiency",
      "ja": "迅速な応答と高効率を持つ高速対話モデル",
      "ko": "Fast conversational model with quick responses and high efficiency",
      "pt": "Fast conversational model with quick responses and high efficiency",
      "ru": "Fast conversational model with quick responses and high efficiency",
      "zh": "快速对话模型，响应迅速，效率高"
    },
    model_name_i18n: {
      "de": "Gemini 2.5 Flash",
      "en": "Gemini 2.5 Flash",
      "es": "Gemini 2.5 Flash",
      "fr": "Gemini 2.5 Flash",
      "it": "Gemini 2.5 Flash",
      "ja": "Gemini 2.5 フラッシュ",
      "ko": "Gemini 2.5 Flash",
      "pt": "Gemini 2.5 Flash",
      "ru": "Gemini 2.5 Flash",
      "zh": "Gemini 2.5 闪电版"
    },
    max_input_size: 128000,
    supported_features: ["text_generation", "conversation", "fast_response"],
    icon: "/imgs/icons/google.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 4,
    model_id: "gpt-4o-mini",
    model_name: "GPT-4o Mini",
    model_type: "text",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 8,
    unit_type: "tokens",
    is_active: true,
    description: "GPT-4o 轻量版本，性能与成本平衡",
    description_i18n: {
      "de": "Compact version of GPT-4o with balanced performance and cost",
      "en": "Compact version of GPT-4o with balanced performance and cost",
      "es": "Compact version of GPT-4o with balanced performance and cost",
      "fr": "Compact version of GPT-4o with balanced performance and cost",
      "it": "Compact version of GPT-4o with balanced performance and cost",
      "ja": "パフォーマンスとコストのバランスが取れたGPT-4oコンパクト版",
      "ko": "Compact version of GPT-4o with balanced performance and cost",
      "pt": "Compact version of GPT-4o with balanced performance and cost",
      "ru": "Compact version of GPT-4o with balanced performance and cost",
      "zh": "GPT-4o 轻量版本，性能与成本平衡"
    },
    model_name_i18n: {
      "de": "GPT-4o Mini",
      "en": "GPT-4o Mini",
      "es": "GPT-4o Mini",
      "fr": "GPT-4o Mini",
      "it": "GPT-4o Mini",
      "ja": "GPT-4o ミニ",
      "ko": "GPT-4o Mini",
      "pt": "GPT-4o Mini",
      "ru": "GPT-4o Mini",
      "zh": "GPT-4o 迷你版"
    },
    max_input_size: 128000,
    supported_features: ["text_generation", "conversation", "reasoning"],
    icon: "/imgs/icons/openai.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 1,
    model_id: "gemini-2.5-pro",
    model_name: "Gemini 2.5 Pro",
    model_type: "text",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 10,
    unit_type: "tokens",
    is_active: true,
    description: "高级对话模型，适合复杂任务和专业用途",
    description_i18n: {
      "de": "Advanced conversational model for complex tasks and professional use",
      "en": "Advanced conversational model for complex tasks and professional use",
      "es": "Advanced conversational model for complex tasks and professional use",
      "fr": "Advanced conversational model for complex tasks and professional use",
      "it": "Advanced conversational model for complex tasks and professional use",
      "ja": "複雑なタスクと専門用途に適した高度な対話モデル",
      "ko": "Advanced conversational model for complex tasks and professional use",
      "pt": "Advanced conversational model for complex tasks and professional use",
      "ru": "Advanced conversational model for complex tasks and professional use",
      "zh": "高级对话模型，适合复杂任务和专业用途"
    },
    model_name_i18n: {
      "de": "Gemini 2.5 Pro",
      "en": "Gemini 2.5 Pro",
      "es": "Gemini 2.5 Pro",
      "fr": "Gemini 2.5 Pro",
      "it": "Gemini 2.5 Pro",
      "ja": "Gemini 2.5 プロ",
      "ko": "Gemini 2.5 Pro",
      "pt": "Gemini 2.5 Pro",
      "ru": "Gemini 2.5 Pro",
      "zh": "Gemini 2.5 专业版"
    },
    max_input_size: 128000,
    supported_features: ["text_generation", "conversation", "analysis"],
    icon: "/imgs/icons/google.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },

  // 多模态模型
  {
    id: 5,
    model_id: "o4-mini-all",
    model_name: "GPT-4o Mini All",
    model_type: "multimodal",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 12,
    unit_type: "tokens",
    is_active: true,
    description: "GPT-4o Mini 全功能版本，支持视觉等多模态能力",
    description_i18n: {
      "de": "GPT-4o Mini with full multimodal capabilities including vision",
      "en": "GPT-4o Mini with full multimodal capabilities including vision",
      "es": "GPT-4o Mini with full multimodal capabilities including vision",
      "fr": "GPT-4o Mini with full multimodal capabilities including vision",
      "it": "GPT-4o Mini with full multimodal capabilities including vision",
      "ja": "ビジョンを含むマルチモーダル機能を持つGPT-4o Mini完全版",
      "ko": "GPT-4o Mini with full multimodal capabilities including vision",
      "pt": "GPT-4o Mini with full multimodal capabilities including vision",
      "ru": "GPT-4o Mini with full multimodal capabilities including vision",
      "zh": "GPT-4o Mini 全功能版本，支持视觉等多模态能力"
    },
    model_name_i18n: {
      "de": "GPT-4o Mini All",
      "en": "GPT-4o Mini All",
      "es": "GPT-4o Mini All",
      "fr": "GPT-4o Mini All",
      "it": "GPT-4o Mini All",
      "ja": "GPT-4o ミニ オール",
      "ko": "GPT-4o Mini All",
      "pt": "GPT-4o Mini All",
      "ru": "GPT-4o Mini All",
      "zh": "GPT-4o 迷你全功能版"
    },
    max_input_size: 128000,
    supported_features: ["text_generation", "vision", "multimodal", "reasoning"],
    icon: "/imgs/icons/openai.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 6,
    model_id: "gpt-4o-all",
    model_name: "GPT-4o All",
    model_type: "multimodal",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 20,
    unit_type: "tokens",
    is_active: true,
    description: "GPT-4o 完整版本，具备所有高级功能和多模态支持",
    description_i18n: {
      "de": "Complete GPT-4o with all advanced features and multimodal support",
      "en": "Complete GPT-4o with all advanced features and multimodal support",
      "es": "Complete GPT-4o with all advanced features and multimodal support",
      "fr": "Complete GPT-4o with all advanced features and multimodal support",
      "it": "Complete GPT-4o with all advanced features and multimodal support",
      "ja": "すべての高度機能とマルチモーダルサポートを持つ完全なGPT-4o",
      "ko": "Complete GPT-4o with all advanced features and multimodal support",
      "pt": "Complete GPT-4o with all advanced features and multimodal support",
      "ru": "Complete GPT-4o with all advanced features and multimodal support",
      "zh": "GPT-4o 完整版本，具备所有高级功能和多模态支持"
    },
    model_name_i18n: {
      "de": "GPT-4o All",
      "en": "GPT-4o All",
      "es": "GPT-4o All",
      "fr": "GPT-4o All",
      "it": "GPT-4o All",
      "ja": "GPT-4o オール",
      "ko": "GPT-4o All",
      "pt": "GPT-4o All",
      "ru": "GPT-4o All",
      "zh": "GPT-4o 全功能版"
    },
    max_input_size: 128000,
    supported_features: ["text_generation", "vision", "multimodal", "advanced_reasoning", "code_generation"],
    icon: "/imgs/icons/openai.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },

  // 图像生成模型
  {
    id: 15,
    model_id: "black-forest-labs/flux-krea-dev",
    model_name: "Flux Krea Dev",
    model_type: "image",
    provider: "replicate",
    api_endpoint: "/replicate/image",
    credits_per_unit: 25,
    unit_type: "images",
    is_active: true,
    description_i18n: {
      "en": "An opinionated text-to-image model from Black Forest Labs in collaboration with Krea that excels in photorealism. Creates images that avoid the oversaturated AI look.",
      "zh": "来自 Black Forest Labs 与 Krea 合作的专业文本转图像模型，擅长生成逼真的照片效果，避免过度饱和的 AI 风格。"
    },
    model_name_i18n: {
      "en": "Flux Krea Dev",
      "zh": "Flux Krea 开发版"
    },
    max_input_size: 2000,
    supported_features: ["text2image", "img2img", "photorealism", "high_quality", "image_upload"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-07-31T17:06:11.205885Z",
    updated_at: "2025-07-31T17:06:11.205885Z"
  },
  {
    id: 9,
    model_id: "flux-pro-1.1",
    model_name: "Flux Pro 1.1",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 30,
    unit_type: "images",
    is_active: true,
    description: "Flux 技术 v1.1 专业图像生成",
    description_i18n: {
      "de": "Professional image generation with Flux technology v1.1",
      "en": "Professional image generation with Flux technology v1.1",
      "es": "Professional image generation with Flux technology v1.1",
      "fr": "Professional image generation with Flux technology v1.1",
      "it": "Professional image generation with Flux technology v1.1",
      "ja": "Flux技術v1.1によるプロフェッショナル画像生成",
      "ko": "Professional image generation with Flux technology v1.1",
      "pt": "Professional image generation with Flux technology v1.1",
      "ru": "Professional image generation with Flux technology v1.1",
      "zh": "Flux 技术 v1.1 专业图像生成"
    },
    model_name_i18n: {
      "de": "Flux Pro 1.1",
      "en": "Flux Pro 1.1",
      "es": "Flux Pro 1.1",
      "fr": "Flux Pro 1.1",
      "it": "Flux Pro 1.1",
      "ja": "Flux プロ 1.1",
      "ko": "Flux Pro 1.1",
      "pt": "Flux Pro 1.1",
      "ru": "Flux Pro 1.1",
      "zh": "Flux 专业版 1.1"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "professional", "flux_tech"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 8,
    model_id: "gpt-4o-image",
    model_name: "GPT-4o Image",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 40,
    unit_type: "images",
    is_active: true,
    description: "使用 GPT-4o 架构的高质量图像生成",
    description_i18n: {
      "de": "High-quality image generation using GPT-4o architecture",
      "en": "High-quality image generation using GPT-4o architecture",
      "es": "High-quality image generation using GPT-4o architecture",
      "fr": "High-quality image generation using GPT-4o architecture",
      "it": "High-quality image generation using GPT-4o architecture",
      "ja": "GPT-4oアーキテクチャを使用した高品質画像生成",
      "ko": "High-quality image generation using GPT-4o architecture",
      "pt": "High-quality image generation using GPT-4o architecture",
      "ru": "High-quality image generation using GPT-4o architecture",
      "zh": "使用 GPT-4o 架构的高质量图像生成"
    },
    model_name_i18n: {
      "de": "GPT-4o Image",
      "en": "GPT-4o Image",
      "es": "GPT-4o Image",
      "fr": "GPT-4o Image",
      "it": "GPT-4o Image",
      "ja": "GPT-4o 画像生成",
      "ko": "GPT-4o Image",
      "pt": "GPT-4o Image",
      "ru": "GPT-4o Image",
      "zh": "GPT-4o 图像生成"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "gpt_powered", "versatile"],
    icon: "/imgs/icons/openai.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 11,
    model_id: "flux-kontext-pro",
    model_name: "Flux Kontext Pro",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 45,
    unit_type: "images",
    is_active: true,
    description: "上下文感知的专业级图像生成",
    description_i18n: {
      "de": "Context-aware image generation with professional quality",
      "en": "Context-aware image generation with professional quality",
      "es": "Context-aware image generation with professional quality",
      "fr": "Context-aware image generation with professional quality",
      "it": "Context-aware image generation with professional quality",
      "ja": "コンテキスト認識によるプロフェッショナル品質画像生成",
      "ko": "Context-aware image generation with professional quality",
      "pt": "Context-aware image generation with professional quality",
      "ru": "Context-aware image generation with professional quality",
      "zh": "上下文感知的专业级图像生成"
    },
    model_name_i18n: {
      "de": "Flux Kontext Pro",
      "en": "Flux Kontext Pro",
      "es": "Flux Kontext Pro",
      "fr": "Flux Kontext Pro",
      "it": "Flux Kontext Pro",
      "ja": "Flux コンテキスト プロ",
      "ko": "Flux Kontext Pro",
      "pt": "Flux Kontext Pro",
      "ru": "Flux Kontext Pro",
      "zh": "Flux 上下文专业版"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "context_aware", "professional"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 7,
    model_id: "sora-image",
    model_name: "Sora Image",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 50,
    unit_type: "images",
    is_active: true,
    description: "基于 Sora 技术的先进图像生成模型",
    description_i18n: {
      "de": "Advanced image generation model powered by Sora technology",
      "en": "Advanced image generation model powered by Sora technology",
      "es": "Advanced image generation model powered by Sora technology",
      "fr": "Advanced image generation model powered by Sora technology",
      "it": "Advanced image generation model powered by Sora technology",
      "ja": "Sora技術による先進的な画像生成モデル",
      "ko": "Advanced image generation model powered by Sora technology",
      "pt": "Advanced image generation model powered by Sora technology",
      "ru": "Advanced image generation model powered by Sora technology",
      "zh": "基于 Sora 技术的先进图像生成模型"
    },
    model_name_i18n: {
      "de": "Sora Image",
      "en": "Sora Image",
      "es": "Sora Image",
      "fr": "Sora Image",
      "it": "Sora Image",
      "ja": "Sora 画像生成",
      "ko": "Sora Image",
      "pt": "Sora Image",
      "ru": "Sora Image",
      "zh": "Sora 图像生成"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "high_quality", "creative"],
    icon: "/imgs/icons/openai.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 10,
    model_id: "flux-pro-1.1-ultra",
    model_name: "Flux Pro 1.1 Ultra",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 60,
    unit_type: "images",
    is_active: true,
    description: "增强版 Flux Pro 超高质量图像生成",
    description_i18n: {
      "de": "Ultra-high quality image generation with enhanced Flux Pro",
      "en": "Ultra-high quality image generation with enhanced Flux Pro",
      "es": "Ultra-high quality image generation with enhanced Flux Pro",
      "fr": "Ultra-high quality image generation with enhanced Flux Pro",
      "it": "Ultra-high quality image generation with enhanced Flux Pro",
      "ja": "強化されたFlux Proによる超高品質画像生成",
      "ko": "Ultra-high quality image generation with enhanced Flux Pro",
      "pt": "Ultra-high quality image generation with enhanced Flux Pro",
      "ru": "Ultra-high quality image generation with enhanced Flux Pro",
      "zh": "增强版 Flux Pro 超高质量图像生成"
    },
    model_name_i18n: {
      "de": "Flux Pro 1.1 Ultra",
      "en": "Flux Pro 1.1 Ultra",
      "es": "Flux Pro 1.1 Ultra",
      "fr": "Flux Pro 1.1 Ultra",
      "it": "Flux Pro 1.1 Ultra",
      "ja": "Flux ウルトラ 1.1",
      "ko": "Flux Pro 1.1 Ultra",
      "pt": "Flux Pro 1.1 Ultra",
      "ru": "Flux Pro 1.1 Ultra",
      "zh": "Flux 超级版 1.1"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "ultra_quality", "enhanced_flux"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 12,
    model_id: "flux-kontext-max",
    model_name: "Flux Kontext Max",
    model_type: "image",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 80,
    unit_type: "images",
    is_active: true,
    description: "最高质量的上下文感知图像生成",
    description_i18n: {
      "de": "Maximum quality context-aware image generation",
      "en": "Maximum quality context-aware image generation",
      "es": "Maximum quality context-aware image generation",
      "fr": "Maximum quality context-aware image generation",
      "it": "Maximum quality context-aware image generation",
      "ja": "最高品質のコンテキスト認識画像生成",
      "ko": "Maximum quality context-aware image generation",
      "pt": "Maximum quality context-aware image generation",
      "ru": "Maximum quality context-aware image generation",
      "zh": "最高质量的上下文感知图像生成"
    },
    model_name_i18n: {
      "de": "Flux Kontext Max",
      "en": "Flux Kontext Max",
      "es": "Flux Kontext Max",
      "fr": "Flux Kontext Max",
      "it": "Flux Kontext Max",
      "ja": "Flux コンテキスト マックス",
      "ko": "Flux Kontext Max",
      "pt": "Flux Kontext Max",
      "ru": "Flux Kontext Max",
      "zh": "Flux 上下文最大版"
    },
    max_input_size: 4000,
    supported_features: ["image_generation", "max_quality", "context_aware"],
    icon: "/imgs/icons/flux.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },

  // 视频生成模型
  {
    id: 13,
    model_id: "veo3-fast",
    model_name: "Veo3 Fast",
    model_type: "video",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 100,
    unit_type: "videos",
    is_active: true,
    description: "Veo3 技术快速视频生成，结果迅速",
    description_i18n: {
      "de": "Fast video generation with Veo3 technology for quick results",
      "en": "Fast video generation with Veo3 technology for quick results",
      "es": "Fast video generation with Veo3 technology for quick results",
      "fr": "Fast video generation with Veo3 technology for quick results",
      "it": "Fast video generation with Veo3 technology for quick results",
      "ja": "迅速な結果を得るVeo3技術による高速動画生成",
      "ko": "Fast video generation with Veo3 technology for quick results",
      "pt": "Fast video generation with Veo3 technology for quick results",
      "ru": "Fast video generation with Veo3 technology for quick results",
      "zh": "Veo3 技术快速视频生成，结果迅速"
    },
    model_name_i18n: {
      "de": "Veo3 Fast",
      "en": "Veo3 Fast",
      "es": "Veo3 Fast",
      "fr": "Veo3 Fast",
      "it": "Veo3 Fast",
      "ja": "Veo3 ファスト",
      "ko": "Veo3 Fast",
      "pt": "Veo3 Fast",
      "ru": "Veo3 Fast",
      "zh": "Veo3 快速版"
    },
    max_input_size: 2000,
    supported_features: ["video_generation", "fast", "veo3_tech"],
    icon: "/imgs/icons/google.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  },
  {
    id: 14,
    model_id: "veo3-pro",
    model_name: "Veo3 Pro",
    model_type: "video",
    provider: "grsai",
    api_endpoint: "/api/ai/generate",
    credits_per_unit: 200,
    unit_type: "videos",
    is_active: true,
    description: "具备高级 Veo3 能力的专业视频生成",
    description_i18n: {
      "de": "Professional video generation with advanced Veo3 capabilities",
      "en": "Professional video generation with advanced Veo3 capabilities",
      "es": "Professional video generation with advanced Veo3 capabilities",
      "fr": "Professional video generation with advanced Veo3 capabilities",
      "it": "Professional video generation with advanced Veo3 capabilities",
      "ja": "高度なVeo3機能を持つプロフェッショナル動画生成",
      "ko": "Professional video generation with advanced Veo3 capabilities",
      "pt": "Professional video generation with advanced Veo3 capabilities",
      "ru": "Professional video generation with advanced Veo3 capabilities",
      "zh": "具备高级 Veo3 能力的专业视频生成"
    },
    model_name_i18n: {
      "de": "Veo3 Pro",
      "en": "Veo3 Pro",
      "es": "Veo3 Pro",
      "fr": "Veo3 Pro",
      "it": "Veo3 Pro",
      "ja": "Veo3 プロ",
      "ko": "Veo3 Pro",
      "pt": "Veo3 Pro",
      "ru": "Veo3 Pro",
      "zh": "Veo3 专业版"
    },
    max_input_size: 2000,
    supported_features: ["video_generation", "professional", "advanced_veo3"],
    icon: "/imgs/icons/google.svg",
    created_at: "2025-08-01T03:06:14.130630Z",
    updated_at: "2025-08-01T03:06:14.130630Z"
  }
];

/**
 * 处理本地化内容的工具函数
 */
export function getLocalizedContent(
  i18nContent: Record<string, string> | undefined,
  locale: string,
  fallbackLocale: string = 'en'
): string {
  if (!i18nContent) return '';
  return i18nContent[locale] || i18nContent[fallbackLocale] || Object.values(i18nContent)[0] || '';
}

/**
 * 处理模型的本地化信息
 */
export function processLocalModelWithLocalization(model: LocalAIModel, locale: string = 'en'): LocalAIModel {
  return {
    ...model,
    model_name: getLocalizedContent(model.model_name_i18n, locale, 'en') || model.model_name,
    description: getLocalizedContent(model.description_i18n, locale, 'en') || model.description || ''
  };
}

/**
 * 获取所有活跃的AI模型（本地版本）
 */
export function getLocalActiveAIModels(locale: string = 'en'): LocalAIModel[] {
  return LOCAL_AI_MODELS
    .filter(model => model.is_active)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => {
      // 先按模型类型排序，再按积分消耗排序
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}

/**
 * 根据模型类型获取模型列表（本地版本）
 */
export function getLocalAIModelsByType(type: string, locale: string = 'en'): LocalAIModel[] {
  return LOCAL_AI_MODELS
    .filter(model => model.is_active && model.model_type === type)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => a.credits_per_unit - b.credits_per_unit);
}

/**
 * 根据模型ID获取单个模型（本地版本）
 */
export function getLocalAIModelById(modelId: string, locale: string = 'en'): LocalAIModel | null {
  const model = LOCAL_AI_MODELS.find(m => m.model_id === modelId && m.is_active);
  return model ? processLocalModelWithLocalization(model, locale) : null;
}

/**
 * 根据模型ID获取单个模型（包括不活跃的模型，本地版本）
 */
export function getLocalAIModelByIdIncludeInactive(modelId: string, locale: string = 'en'): LocalAIModel | null {
  const model = LOCAL_AI_MODELS.find(m => m.model_id === modelId);
  return model ? processLocalModelWithLocalization(model, locale) : null;
}

/**
 * 根据提供商获取模型列表（本地版本）
 */
export function getLocalAIModelsByProvider(provider: string, locale: string = 'en'): LocalAIModel[] {
  return LOCAL_AI_MODELS
    .filter(model => model.is_active && model.provider === provider)
    .map(model => processLocalModelWithLocalization(model, locale))
    .sort((a, b) => {
      if (a.model_type !== b.model_type) {
        const typeOrder = { text: 0, multimodal: 1, image: 2, video: 3 };
        return (typeOrder[a.model_type] || 999) - (typeOrder[b.model_type] || 999);
      }
      return a.credits_per_unit - b.credits_per_unit;
    });
}
