"use client";

import { useState } from "react";
import { ChevronDown, Setting<PERSON>, Zap, Wrench } from "lucide-react";
import { ParameterConfig, PARAMETER_GROUP_TITLES } from "@/config/parameter-types";
import { ParameterInput } from "./ParameterInput";
import { shouldShowParameter } from "@/lib/model-parameter-loader";
import { useDeviceLayout } from "../../../hooks/use-device-layout";

interface ParameterGroupProps {
  groupName: 'basic' | 'advanced' | 'expert';
  parameters: ParameterConfig[];
  values: Record<string, any>;
  onChange: (name: string, value: any) => void;
  errors?: Record<string, string>;
  defaultCollapsed?: boolean;
  modelType?: string; // 新增：模型类型
}

/**
 * 参数组图标映射
 */
const GROUP_ICONS = {
  basic: Settings,
  advanced: Zap,
  expert: Wrench
} as const;

/**
 * 参数组颜色映射
 */
const GROUP_COLORS = {
  basic: 'from-blue-500/10 to-blue-600/5 border-blue-500/20',
  advanced: 'from-orange-500/10 to-orange-600/5 border-orange-500/20',
  expert: 'from-red-500/10 to-red-600/5 border-red-500/20'
} as const;

export function ParameterGroup({
  groupName,
  parameters,
  values,
  onChange,
  errors,
  defaultCollapsed = false,
  modelType
}: ParameterGroupProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);

  // 过滤显示的参数（基于条件依赖）
  const visibleParameters = parameters.filter(param => 
    shouldShowParameter(param, values)
  );

  // 如果没有可见参数，不显示这个组
  if (visibleParameters.length === 0) {
    return null;
  }

  const Icon = GROUP_ICONS[groupName];
  const title = PARAMETER_GROUP_TITLES[groupName];
  const colorClass = GROUP_COLORS[groupName];

  // 检查是否有错误
  const hasErrors = visibleParameters.some(param => errors?.[param.name]);

  return (
    <div className={`border rounded-xl overflow-hidden bg-gradient-to-r ${colorClass} ${
      hasErrors ? 'border-destructive/30' : ''
    }`}>
      {/* 组标题 */}
      <button
        onClick={() => setIsCollapsed(!isCollapsed)}
        className={`w-full flex items-center justify-between transition-colors ${
          isSmallMobile ? 'px-3 py-2' : isMobile ? 'px-4 py-2.5' : 'px-4 py-3'
        } hover:bg-muted/20`}
      >
        <div className="flex items-center gap-2">
          <Icon className={`${
            isSmallMobile ? 'w-4 h-4' : 'w-5 h-5'
          } text-foreground`} />
          <h3 className={`font-semibold text-left text-foreground ${
            isSmallMobile ? 'text-sm' : isMobile ? 'text-base' : 'text-base'
          }`}>
            {title}
          </h3>
          <span className={`text-muted-foreground ${
            isSmallMobile ? 'text-xs' : 'text-sm'
          }`}>
            ({visibleParameters.length})
          </span>
          {hasErrors && (
            <span className="w-2 h-2 bg-destructive rounded-full"></span>
          )}
        </div>
        
        <ChevronDown className={`${
          isSmallMobile ? 'w-4 h-4' : 'w-5 h-5'
        } text-muted-foreground transition-transform duration-200 ${
          isCollapsed ? 'rotate-180' : ''
        }`} />
      </button>

      {/* 参数列表 */}
      {!isCollapsed && (
        <div className={`border-t border-border/30 bg-background/50 ${
          isSmallMobile ? 'p-3 space-y-3' : isMobile ? 'p-4 space-y-4' : 'p-4 space-y-4'
        }`}>
          {visibleParameters.map(param => (
            <div key={param.name} className="w-full">
              <ParameterInput
                config={param}
                value={values[param.name]}
                onChange={(value) => onChange(param.name, value)}
                error={errors?.[param.name]}
                modelType={modelType}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
