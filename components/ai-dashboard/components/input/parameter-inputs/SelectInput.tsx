"use client";

import { Label } from "@/components/ui/label";
import { HelpCircle } from "lucide-react";
import {
  RichSelect,
  RichSelectContent,
  RichSelectItem,
  RichSelectTrigger,
  RichSelectValue,
} from "@/components/ui/rich-select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/config/parameter-types";

interface SelectInputProps {
  config: ParameterConfig;
  value: string | undefined;
  onChange: (value: string) => void;
  error?: string;
}

export function SelectInput({ config, value, onChange, error }: SelectInputProps) {
  const { isMobile, isSmallMobile } = useDeviceLayout();

  const currentValue = value || config.default || '';
  const placeholder = config.required ? '请选择...' : '请选择（可选）';

  return (
    <div className="space-y-2">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {config.description}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 选择器 */}
      <RichSelect value={currentValue} onValueChange={onChange}>
        <RichSelectTrigger 
          size={isMobile ? "default" : "lg"}
          className={`
            w-full bg-gradient-to-r from-background to-muted/30 
            border-border/50 hover:border-border rounded-xl
            ${error ? 'border-destructive focus:border-destructive' : ''}
            ${isSmallMobile ? 'text-xs h-8' : isMobile ? 'text-sm h-9' : 'text-sm h-10'}
          `}
        >
          <RichSelectValue placeholder={placeholder}>
            {currentValue && config.options && (
              <div className={`flex items-center ${isSmallMobile ? 'gap-1.5' : 'gap-2'}`}>
                <div className={`flex flex-col items-start text-left min-w-0 flex-1 ${
                  isSmallMobile ? 'gap-0' : 'gap-0.5'
                }`}>
                  <span className={`font-medium truncate w-full ${
                    isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
                  }`}>
                    {config.options.find(opt => opt.value === currentValue)?.label || currentValue}
                  </span>
                  {!isSmallMobile && config.options.find(opt => opt.value === currentValue)?.description && (
                    <span className="text-xs text-muted-foreground truncate w-full">
                      {config.options.find(opt => opt.value === currentValue)?.description}
                    </span>
                  )}
                </div>
              </div>
            )}
          </RichSelectValue>
        </RichSelectTrigger>
        
        <RichSelectContent className="z-[150]">
          {config.options?.map((option) => (
            <RichSelectItem 
              key={option.value} 
              value={option.value}
              option={{
                value: option.value,
                label: option.label,
                description: option.description
              }}
            >
              <div className="flex flex-col">
                <span className="font-medium">{option.label}</span>
                {option.description && (
                  <span className="text-xs text-muted-foreground">
                    {option.description}
                  </span>
                )}
              </div>
            </RichSelectItem>
          ))}
        </RichSelectContent>
      </RichSelect>

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}
    </div>
  );
}
