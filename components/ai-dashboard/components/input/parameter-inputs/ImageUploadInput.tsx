"use client";

import { useState, useRef, useEffect } from "react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Upload, X, HelpCircle } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../../hooks/use-device-layout";
import { ParameterConfig } from "@/config/parameter-types";

interface ImageUploadInputProps {
  config: ParameterConfig;
  value: string[] | undefined;
  onChange: (value: string[]) => void;
  error?: string;
  modelType?: string;
}

export function ImageUploadInput({ 
  config, 
  value, 
  onChange, 
  error,
  modelType = 'image'
}: ImageUploadInputProps) {
  const t = useTranslations("ai-dashboard");
  const { isMobile, isSmallMobile } = useDeviceLayout();
  
  const [uploadedImages, setUploadedImages] = useState<string[]>(value || []);
  const [uploading, setUploading] = useState(false);
  const [isDragOver, setIsDragOver] = useState(false);
  const [dragCounter, setDragCounter] = useState(0);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 同步外部值变化到本地状态
  useEffect(() => {
    setUploadedImages(value || []);
  }, [value]);

  // 防止页面默认的拖放行为
  useEffect(() => {
    const preventDefault = (e: DragEvent) => {
      e.preventDefault();
    };

    const handlePageDrop = (e: DragEvent) => {
      e.preventDefault();
      if (!dropZoneRef.current?.contains(e.target as Node)) {
        return false;
      }
    };

    document.addEventListener('dragover', preventDefault);
    document.addEventListener('drop', handlePageDrop);

    return () => {
      document.removeEventListener('dragover', preventDefault);
      document.removeEventListener('drop', handlePageDrop);
    };
  }, []);

  // 处理文件上传的通用函数
  const processFileUpload = async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert(t("errors.invalid_input"));
      return;
    }

    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await fetch('/api/ai/upload-image', {
        method: 'POST',
        body: formData,
      });

      const result = await response.json();

      if (result.code === 0) {
        const newImages = [...uploadedImages, result.data.url];
        console.log('[ImageUpload] New images:', newImages);
        setUploadedImages(newImages);
        onChange(newImages);
      } else {
        alert(result.msg || t("errors.network_error"));
      }
    } catch (error) {
      console.error('Upload error:', error);
      alert(t("errors.network_error"));
    } finally {
      setUploading(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    const file = files[0];
    await processFileUpload(file);
  };

  // 拖放事件处理函数
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => prev + 1);

    if (e.dataTransfer.types.includes('Files')) {
      setIsDragOver(true);
    }
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setDragCounter(prev => {
      const newCount = prev - 1;
      if (newCount === 0) {
        setIsDragOver(false);
      }
      return newCount;
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (e.dataTransfer.types.includes('Files')) {
      e.dataTransfer.dropEffect = 'copy';
    }
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsDragOver(false);
    setDragCounter(0);

    const files = Array.from(e.dataTransfer.files);
    if (files.length === 0) return;

    const imageFiles = files.filter(file => file.type.startsWith('image/'));

    if (imageFiles.length === 0) {
      alert(t("errors.invalid_input"));
      return;
    }

    if (imageFiles.length > 1) {
      alert(`检测到 ${imageFiles.length} 个图片文件，将上传第一个：${imageFiles[0].name}`);
    }

    await processFileUpload(imageFiles[0]);
  };

  const removeImage = (index: number) => {
    const newImages = uploadedImages.filter((_, i) => i !== index);
    setUploadedImages(newImages);
    onChange(newImages);
  };

  // 根据模型类型确定标签文本
  const getLabelText = () => {
    switch (modelType) {
      case 'video':
        return t("options.first_frame");
      case 'image':
        return t("options.reference_image");
      default:
        return config.description || t("options.image_upload");
    }
  };

  return (
    <div className="space-y-2">
      {/* 标签和帮助图标 */}
      <div className="flex items-center gap-2">
        <Label 
          className={`font-medium text-foreground ${
            isSmallMobile ? 'text-xs' : isMobile ? 'text-sm' : 'text-sm'
          }`}
        >
          {getLabelText()}
          {config.required && <span className="text-destructive ml-1">*</span>}
        </Label>
        
        {config.tooltip && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className={`text-muted-foreground cursor-help ${
                  isSmallMobile ? 'w-3 h-3' : 'w-4 h-4'
                }`} />
              </TooltipTrigger>
              <TooltipContent side="top" className="max-w-xs">
                <p className="text-sm">{config.tooltip}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>

      {/* 拖放上传区域 */}
      <div
        ref={dropZoneRef}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
        className={`
          relative border-2 border-dashed rounded-xl transition-all duration-300 cursor-pointer
          ${isDragOver
            ? 'border-primary bg-primary/10 scale-[1.02] shadow-lg shadow-primary/20'
            : 'border-border/50 hover:border-border hover:bg-muted/20'
          }
          ${uploading ? 'pointer-events-none opacity-50' : ''}
          ${error ? 'border-destructive/50' : ''}
          ${isSmallMobile ? 'p-4' : 'p-6'}
          group
        `}
        onClick={() => !uploading && fileInputRef.current?.click()}
      >
        <div className="flex flex-col items-center justify-center gap-3 text-center">
          <div className={`
            rounded-full transition-all duration-300
            ${isDragOver
              ? 'bg-primary text-primary-foreground scale-110 animate-pulse'
              : 'bg-muted group-hover:bg-muted/80'
            }
            ${isSmallMobile ? 'p-2' : 'p-3'}
          `}>
            <Upload className={`text-current transition-transform duration-300 ${
              isDragOver ? 'scale-110' : 'group-hover:scale-105'
            } ${isSmallMobile ? 'w-5 h-5' : 'w-6 h-6'}`} />
          </div>

          <div className="space-y-1">
            <p className={`font-medium transition-colors duration-300 ${
              isDragOver ? 'text-primary' : 'text-foreground'
            } ${isSmallMobile ? 'text-sm' : 'text-base'}`}>
              {uploading ? t("options.uploading") :
               isDragOver ? t("options.drop_to_upload") : t("options.drag_drop")}
            </p>
            <p className={`text-muted-foreground ${
              isSmallMobile ? 'text-xs' : 'text-sm'
            }`}>
              {t("options.supported_formats")}
            </p>
            {isDragOver && (
              <p className={`text-primary font-medium animate-bounce ${
                isSmallMobile ? 'text-xs' : 'text-sm'
              }`}>
                {t("options.file_detected")}
              </p>
            )}
          </div>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleImageUpload}
          className="hidden"
        />
      </div>

      {/* 已上传图片预览 */}
      {uploadedImages.length > 0 && (
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
          {uploadedImages.map((imageUrl, index) => (
            <div key={index} className="relative group">
              <img
                src={imageUrl}
                alt={`${getLabelText()} ${index + 1}`}
                className="w-full h-20 object-cover rounded-lg border border-border/50"
              />
              <Button
                type="button"
                variant="destructive"
                size="sm"
                onClick={() => removeImage(index)}
                className="absolute top-1 right-1 w-6 h-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <p className={`text-destructive ${
          isSmallMobile ? 'text-xs' : 'text-sm'
        }`}>
          {error}
        </p>
      )}
    </div>
  );
}
