"use client";

import { ParameterConfig } from "@/config/parameter-types";
import { NumberInput } from "./NumberInput";
import { SelectInput } from "./SelectInput";
import { BooleanInput } from "./BooleanInput";
import { RangeInput } from "./RangeInput";
import { TextInput } from "./TextInput";
import { ImageUploadInput } from "./ImageUploadInput";

interface ParameterInputProps {
  config: ParameterConfig;
  value: any;
  onChange: (value: any) => void;
  error?: string;
  modelType?: string; // 新增：模型类型，用于图片上传组件
}

/**
 * 通用参数输入组件
 * 根据参数配置自动选择合适的输入组件
 */
export function ParameterInput({ config, value, onChange, error, modelType }: ParameterInputProps) {
  // 根据参数类型选择对应的输入组件
  switch (config.type) {
    case 'number':
      return (
        <NumberInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );

    case 'select':
      return (
        <SelectInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );

    case 'boolean':
      return (
        <BooleanInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );

    case 'range':
      return (
        <RangeInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
        />
      );

    case 'string':
      return (
        <TextInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
          multiline={false}
        />
      );

    case 'file':
      // 如果是图片上传类型，使用图片上传组件
      if (config.name === 'uploadedImages' || config.name.includes('image')) {
        return (
          <ImageUploadInput
            config={config}
            value={value}
            onChange={onChange}
            error={error}
            modelType={modelType}
          />
        );
      }
      // 其他文件类型暂时使用文本输入
      return (
        <TextInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
          multiline={false}
        />
      );

    default:
      // 默认使用文本输入
      console.warn(`Unknown parameter type: ${config.type}, falling back to text input`);
      return (
        <TextInput
          config={config}
          value={value}
          onChange={onChange}
          error={error}
          multiline={false}
        />
      );
  }
}
