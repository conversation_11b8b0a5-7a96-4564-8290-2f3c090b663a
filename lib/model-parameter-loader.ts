/**
 * 模型参数配置加载器
 */

import {
  getModelParameterConfig,
  getModelDefaultParameters,
  validateParameterValue,
  hasParameterConfig
} from '@/config/model-parameters';
import { ModelParameterConfig, ParameterConfig } from '@/config/parameter-types';

// 重新导出核心函数，方便其他组件使用
export {
  getModelParameterConfig,
  getModelDefaultParameters,
  validateParameterValue,
  hasParameterConfig
} from '@/config/model-parameters';

/**
 * React Hook: 获取模型参数配置
 */
export function useModelParameterConfig(modelId: string | null): ModelParameterConfig | null {
  if (!modelId) return null;
  return getModelParameterConfig(modelId);
}

/**
 * 获取模型的默认参数值
 */
export function getModelDefaults(modelId: string): Record<string, any> {
  return getModelDefaultParameters(modelId);
}

/**
 * 验证参数值
 */
export function validateParameter(
  modelId: string, 
  paramName: string, 
  value: any
): { valid: boolean; error?: string } {
  return validateParameterValue(modelId, paramName, value);
}

/**
 * 验证所有参数
 */
export function validateAllParameters(
  modelId: string, 
  values: Record<string, any>
): { valid: boolean; errors: Record<string, string> } {
  const config = getModelParameterConfig(modelId);
  if (!config) {
    return { valid: false, errors: { _general: '模型配置不存在' } };
  }

  const errors: Record<string, string> = {};
  let hasErrors = false;

  config.parameters.forEach(param => {
    const value = values[param.name];
    const result = validateParameterValue(modelId, param.name, value);

    // 添加调试信息
    if (!result.valid && result.error) {
      console.log(`[Validation] Parameter ${param.name} failed:`, {
        value,
        param,
        error: result.error
      });
      errors[param.name] = result.error;
      hasErrors = true;
    }
  });

  return { valid: !hasErrors, errors };
}

/**
 * 检查参数是否应该显示（基于条件依赖）
 */
export function shouldShowParameter(
  param: ParameterConfig, 
  values: Record<string, any>
): boolean {
  if (!param.condition) return true;

  const dependentValue = values[param.condition.field];
  
  switch (param.condition.operator) {
    case 'eq':
      return dependentValue === param.condition.value;
    case 'ne':
      return dependentValue !== param.condition.value;
    case 'gt':
      return typeof dependentValue === 'number' && dependentValue > param.condition.value;
    case 'lt':
      return typeof dependentValue === 'number' && dependentValue < param.condition.value;
    case 'in':
      return Array.isArray(param.condition.value) && 
             param.condition.value.includes(dependentValue);
    case 'not_empty':
      if (Array.isArray(dependentValue)) {
        return dependentValue.length > 0;
      }
      return dependentValue !== undefined && dependentValue !== null && dependentValue !== '';
    default:
      return true;
  }
}

/**
 * 过滤显示的参数（基于条件依赖）
 */
export function getVisibleParameters(
  parameters: ParameterConfig[], 
  values: Record<string, any>
): ParameterConfig[] {
  return parameters.filter(param => shouldShowParameter(param, values));
}

/**
 * 合并默认值和用户值
 */
export function mergeWithDefaults(
  modelId: string, 
  userValues: Record<string, any>
): Record<string, any> {
  const defaults = getModelDefaultParameters(modelId);
  return { ...defaults, ...userValues };
}

/**
 * 清理参数值（移除undefined和null）
 */
export function cleanParameterValues(values: Record<string, any>): Record<string, any> {
  const cleaned: Record<string, any> = {};
  
  Object.entries(values).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      cleaned[key] = value;
    }
  });
  
  return cleaned;
}

/**
 * 检查模型是否支持参数配置
 */
export function isModelSupported(modelId: string): boolean {
  return hasParameterConfig(modelId);
}

/**
 * 获取参数的显示值（用于UI显示）
 */
export function getParameterDisplayValue(
  param: ParameterConfig, 
  value: any
): string {
  if (value === undefined || value === null || value === '') {
    return param.default?.toString() || '';
  }

  if (param.type === 'select' && param.options) {
    const option = param.options.find(opt => opt.value === value);
    return option ? option.label : value.toString();
  }

  if (param.type === 'boolean') {
    return value ? '启用' : '禁用';
  }

  return value.toString();
}

/**
 * 转换参数值为API所需的格式
 */
export function convertParameterForAPI(
  param: ParameterConfig, 
  value: any
): any {
  if (value === undefined || value === null || value === '') {
    return param.default;
  }

  switch (param.type) {
    case 'number':
      return typeof value === 'number' ? value : parseFloat(value);
    case 'boolean':
      if (typeof value === 'boolean') return value;
      if (typeof value === 'string') return value === 'true';
      return Boolean(value);
    case 'select':
      return value.toString();
    default:
      return value;
  }
}

/**
 * 将所有参数值转换为API格式
 */
export function convertAllParametersForAPI(
  modelId: string, 
  values: Record<string, any>
): Record<string, any> {
  const config = getModelParameterConfig(modelId);
  if (!config) return values;

  const converted: Record<string, any> = {};
  
  config.parameters.forEach(param => {
    const value = values[param.name];
    const convertedValue = convertParameterForAPI(param, value);
    
    if (convertedValue !== undefined) {
      converted[param.name] = convertedValue;
    }
  });

  return converted;
}
